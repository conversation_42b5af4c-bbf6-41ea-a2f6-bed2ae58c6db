"""
Signal Synthesizer for comprehensive stock analysis.

This service combines multiple technical indicators and analysis methods
to provide comprehensive trading recommendations, zones, and risk analysis.
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any

from shared.models.stock_models import (
    PricePoint, TechnicalIndicator, TrendAnalysis, TradingZone, 
    RiskRewardRatio, SignalType, ConfidenceLevel, MarketCondition,
    TrendDirection
)
from shared.exceptions.stock_exceptions import AnalysisException

# Import legacy components for comprehensive analysis
try:
    from stockpal.indicator.signal_synthesizer import SignalSynthesizer as LegacySignalSynthesizer
    from stockpal.indicator.trend_predictor import TrendPredictor
    from stockpal.indicator.ichimoku import Ichimo<PERSON>
    from stockpal.indicator.pivot_points import PivotPoints
    from stockpal.core.stock import PriceData
    LEGACY_AVAILABLE = True
except ImportError:
    LEGACY_AVAILABLE = False


logger = logging.getLogger(__name__)


class SignalSynthesizer:
    """
    Synthesizes signals from multiple indicators to provide comprehensive trading recommendations.
    
    This service integrates multiple technical indicators with adaptive weighting to determine:
    - Current trend direction and strength
    - Support and resistance levels
    - Optimal buy, stop loss, and take profit zones
    - Final trading recommendation with confidence level
    """

    def __init__(self):
        """Initialize the signal synthesizer."""
        self._logger = logger

    def synthesize_analysis(
        self,
        symbol: str,
        prices: List[PricePoint],
        technical_indicators: List[TechnicalIndicator],
        trend_analysis: TrendAnalysis,
        include_zones: bool = True,
        include_risk_analysis: bool = True
    ) -> Dict[str, Any]:
        """
        Perform comprehensive signal synthesis and analysis.

        Args:
            symbol: Stock symbol
            prices: Historical price data
            technical_indicators: Calculated technical indicators
            trend_analysis: Trend analysis results
            include_zones: Whether to calculate trading zones
            include_risk_analysis: Whether to perform risk analysis

        Returns:
            Dictionary containing comprehensive analysis results
        """
        try:
            self._logger.info(f"Synthesizing signals for {symbol}")

            if LEGACY_AVAILABLE:
                # Use legacy SignalSynthesizer for comprehensive analysis
                result = self._use_legacy_synthesizer(symbol, prices, include_zones, include_risk_analysis)
            else:
                # Fallback to basic synthesis
                result = self._basic_synthesis(symbol, prices, technical_indicators, trend_analysis, 
                                             include_zones, include_risk_analysis)

            self._logger.info(f"Signal synthesis completed for {symbol}")
            return result

        except Exception as e:
            self._logger.error(f"Failed to synthesize signals for {symbol}: {str(e)}")
            raise AnalysisException(f"Signal synthesis failed: {str(e)}")

    def _use_legacy_synthesizer(
        self,
        symbol: str,
        prices: List[PricePoint],
        include_zones: bool,
        include_risk_analysis: bool
    ) -> Dict[str, Any]:
        """Use legacy SignalSynthesizer for comprehensive analysis."""
        try:
            # Convert to legacy format
            legacy_prices = []
            for price in prices:
                legacy_price = PriceData(
                    timestamp=price.timestamp,
                    open_price=price.open_price,
                    high_price=price.high_price,
                    low_price=price.low_price,
                    close_price=price.close_price,
                    match_volume=price.volume,
                    change_price=price.change_price or 0.0,
                    change_price_percent=price.change_percent or 0.0
                )
                legacy_prices.append(legacy_price)

            # Initialize legacy synthesizer
            synthesizer = LegacySignalSynthesizer(symbol=symbol, prices=legacy_prices, language="vi")
            
            # Perform comprehensive analysis
            analysis_result = synthesizer.analyze()

            # Convert results to new format
            result = {
                "buy_zones": self._convert_legacy_zones(analysis_result.get("buy_zones", []), "buy"),
                "stop_loss_zones": self._convert_legacy_zones(analysis_result.get("stop_loss_zones", []), "stop_loss"),
                "take_profit_zones": self._convert_legacy_zones(analysis_result.get("take_profit_zones", []), "take_profit"),
                "risk_reward_ratios": self._convert_legacy_risk_rewards(analysis_result.get("risk_reward_ratios", [])),
                "recommendation": self._convert_recommendation(analysis_result.get("recommendation", "Giữ")),
                "market_condition": self._convert_market_condition(analysis_result.get("market_condition", "Trung lập")),
                "technical_summary": self._generate_summary_from_legacy(analysis_result),
                "trend_direction": analysis_result.get("trend_direction", "Đi ngang"),
                "trend_strength": analysis_result.get("trend_strength", 0.5),
                "trend_confidence": analysis_result.get("trend_confidence", 50.0),
                "enhanced_signals": analysis_result.get("enhanced_signals", [])
            }

            return result

        except Exception as e:
            self._logger.error(f"Legacy synthesizer failed: {str(e)}")
            # Fallback to basic synthesis
            return self._basic_synthesis(symbol, prices, [], None, include_zones, include_risk_analysis)

    def _basic_synthesis(
        self,
        symbol: str,
        prices: List[PricePoint],
        technical_indicators: List[TechnicalIndicator],
        trend_analysis: Optional[TrendAnalysis],
        include_zones: bool,
        include_risk_analysis: bool
    ) -> Dict[str, Any]:
        """Basic signal synthesis when legacy components are not available."""
        current_price = prices[-1].close_price
        
        # Basic zone calculation
        buy_zones = []
        stop_loss_zones = []
        take_profit_zones = []
        risk_reward_ratios = []

        if include_zones:
            # Simple support/resistance based zones
            recent_prices = [p.close_price for p in prices[-20:]]
            support_level = min(recent_prices)
            resistance_level = max(recent_prices)

            # Buy zone near support
            if current_price > support_level * 1.02:  # 2% above support
                buy_zones.append(TradingZone(
                    price=support_level * 1.01,
                    confidence=ConfidenceLevel.MEDIUM,
                    reason="Near support level",
                    zone_type="buy"
                ))

            # Stop loss below support
            stop_loss_zones.append(TradingZone(
                price=support_level * 0.95,
                confidence=ConfidenceLevel.MEDIUM,
                reason="Below support level",
                zone_type="stop_loss"
            ))

            # Take profit near resistance
            take_profit_zones.append(TradingZone(
                price=resistance_level * 0.98,
                confidence=ConfidenceLevel.MEDIUM,
                reason="Near resistance level",
                zone_type="take_profit"
            ))

        if include_risk_analysis and buy_zones and stop_loss_zones and take_profit_zones:
            # Calculate basic risk-reward ratios
            for buy_zone in buy_zones:
                for stop_zone in stop_loss_zones:
                    for tp_zone in take_profit_zones:
                        risk = buy_zone.price - stop_zone.price
                        reward = tp_zone.price - buy_zone.price
                        if risk > 0:
                            ratio = reward / risk
                            risk_reward_ratios.append(RiskRewardRatio(
                                buy_price=buy_zone.price,
                                stop_loss_price=stop_zone.price,
                                take_profit_price=tp_zone.price,
                                ratio=ratio,
                                quality="Good" if ratio >= 2.0 else "Fair" if ratio >= 1.5 else "Poor"
                            ))

        # Basic recommendation logic
        recommendation = "HOLD"
        if technical_indicators:
            buy_signals = sum(1 for ind in technical_indicators if ind.signal == SignalType.BUY)
            sell_signals = sum(1 for ind in technical_indicators if ind.signal == SignalType.SELL)
            
            if buy_signals > sell_signals and buy_signals >= len(technical_indicators) * 0.6:
                recommendation = "BUY"
            elif sell_signals > buy_signals and sell_signals >= len(technical_indicators) * 0.6:
                recommendation = "SELL"

        return {
            "buy_zones": buy_zones,
            "stop_loss_zones": stop_loss_zones,
            "take_profit_zones": take_profit_zones,
            "risk_reward_ratios": risk_reward_ratios,
            "recommendation": recommendation,
            "market_condition": MarketCondition.NEUTRAL,
            "technical_summary": f"Basic analysis for {symbol} with {len(technical_indicators)} indicators",
            "trend_direction": trend_analysis.direction.value if trend_analysis else "SIDEWAYS",
            "trend_strength": trend_analysis.strength if trend_analysis else 0.5,
            "trend_confidence": 50.0,
            "enhanced_signals": []
        }

    def _convert_legacy_zones(self, legacy_zones: List[Dict], zone_type: str) -> List[TradingZone]:
        """Convert legacy zone format to new TradingZone objects."""
        zones = []
        for zone in legacy_zones:
            if isinstance(zone, dict) and "price" in zone:
                confidence_map = {
                    "high": ConfidenceLevel.HIGH,
                    "medium": ConfidenceLevel.MEDIUM,
                    "low": ConfidenceLevel.LOW
                }
                confidence = confidence_map.get(zone.get("confidence", "medium").lower(), ConfidenceLevel.MEDIUM)
                
                zones.append(TradingZone(
                    price=zone["price"],
                    confidence=confidence,
                    reason=zone.get("reason", f"{zone_type} zone"),
                    zone_type=zone_type
                ))
        return zones

    def _convert_legacy_risk_rewards(self, legacy_ratios: List[Dict]) -> List[RiskRewardRatio]:
        """Convert legacy risk-reward format to new RiskRewardRatio objects."""
        ratios = []
        for ratio in legacy_ratios:
            if isinstance(ratio, dict) and all(k in ratio for k in ["buy_price", "stop_loss_price", "take_profit_price", "ratio"]):
                ratios.append(RiskRewardRatio(
                    buy_price=ratio["buy_price"],
                    stop_loss_price=ratio["stop_loss_price"],
                    take_profit_price=ratio["take_profit_price"],
                    ratio=ratio["ratio"],
                    quality=ratio.get("quality", "Fair")
                ))
        return ratios

    def _convert_recommendation(self, legacy_rec: str) -> str:
        """Convert Vietnamese recommendation to English."""
        mapping = {
            "Mua mạnh": "STRONG_BUY",
            "Mua": "BUY",
            "Giữ": "HOLD",
            "Bán": "SELL",
            "Bán mạnh": "STRONG_SELL"
        }
        return mapping.get(legacy_rec, "HOLD")

    def _convert_market_condition(self, legacy_condition: str) -> MarketCondition:
        """Convert legacy market condition to new enum."""
        mapping = {
            "Tăng mạnh": MarketCondition.BULLISH,
            "Tăng": MarketCondition.BULLISH,
            "Trung lập": MarketCondition.NEUTRAL,
            "Giảm": MarketCondition.BEARISH,
            "Giảm mạnh": MarketCondition.BEARISH
        }
        return mapping.get(legacy_condition, MarketCondition.NEUTRAL)

    def _generate_summary_from_legacy(self, analysis_result: Dict) -> str:
        """Generate technical summary from legacy analysis results."""
        summary_parts = []
        
        if "trend_direction" in analysis_result:
            summary_parts.append(f"Trend: {analysis_result['trend_direction']}")
        
        if "trend_strength" in analysis_result:
            strength = analysis_result["trend_strength"]
            summary_parts.append(f"Strength: {strength:.1%}")
        
        if "recommendation" in analysis_result:
            summary_parts.append(f"Recommendation: {analysis_result['recommendation']}")
        
        buy_zones_count = len(analysis_result.get("buy_zones", []))
        if buy_zones_count > 0:
            summary_parts.append(f"{buy_zones_count} buy zones identified")
        
        return ". ".join(summary_parts) if summary_parts else "Technical analysis completed"
